import requests
import re
import urllib.parse
from urllib.parse import urlparse
import tldextract
import difflib
from bs4 import BeautifulSoup
import pandas as pd
import numpy as np
from typing import Dict, Any
import warnings
warnings.filterwarnings('ignore')

class URLFeatureExtractor:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

    def extract_features(self, url: str) -> Dict[str, Any]:
        """Extract all 30 features from a given URL"""
        try:
            # Ensure URL has protocol
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url

            # Parse URL
            parsed_url = urlparse(url)
            extracted = tldextract.extract(url)

            # Get webpage content
            try:
                response = self.session.get(url, timeout=10, allow_redirects=True)
                html_content = response.text
                soup = BeautifulSoup(html_content, 'html.parser')
            except:
                html_content = ""
                soup = BeautifulSoup("", 'html.parser')

            features = {}

            # 1. TLD
            features['TLD'] = extracted.suffix if extracted.suffix else 'unknown'

            # 2. Title
            title_tag = soup.find('title')
            features['Title'] = title_tag.get_text().strip() if title_tag else ''

            # 3. URLLength
            features['URLLength'] = len(url)

            # 4. URLSimilarityIndex
            features['URLSimilarityIndex'] = self._calculate_url_similarity_index(url)

            # 5. CharContinuationRate
            features['CharContinuationRate'] = self._calculate_char_continuation_rate(url)

            # 6. TLDLegitimateProb
            features['TLDLegitimateProb'] = self._calculate_tld_legitimate_prob(extracted.suffix)

            # 7. NoOfLettersInURL
            features['NoOfLettersInURL'] = sum(1 for c in url if c.isalpha())

            # 8. LetterRatioInURL
            features['LetterRatioInURL'] = features['NoOfLettersInURL'] / len(url) if len(url) > 0 else 0

            # 9. NoOfDegitsInURL
            features['NoOfDegitsInURL'] = sum(1 for c in url if c.isdigit())

            # 10. NoOfOtherSpecialCharsInURL
            features['NoOfOtherSpecialCharsInURL'] = sum(1 for c in url if not c.isalnum() and c not in ':/.-_?&=')

            # 11. IsHTTPS
            features['IsHTTPS'] = 1 if parsed_url.scheme == 'https' else 0

            # 12. LineOfCode
            features['LineOfCode'] = len(html_content.split('\n')) if html_content else 0

            # 13. LargestLineLength
            lines = html_content.split('\n') if html_content else ['']
            features['LargestLineLength'] = max(len(line) for line in lines) if lines else 0

            # 14. HasTitle
            features['HasTitle'] = 1 if title_tag and title_tag.get_text().strip() else 0

            # 15. DomainTitleMatchScore
            features['DomainTitleMatchScore'] = self._calculate_domain_title_match(extracted.domain, features['Title'])

            # 16. URLTitleMatchScore
            features['URLTitleMatchScore'] = self._calculate_url_title_match(url, features['Title'])

            # 17. HasFavicon
            features['HasFavicon'] = self._check_favicon(soup, url)

            # 18. IsResponsive
            features['IsResponsive'] = self._check_responsive(soup)

            # 19. HasDescription
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            features['HasDescription'] = 1 if meta_desc and meta_desc.get('content', '').strip() else 0

            # 20. NoOfiFrame
            features['NoOfiFrame'] = len(soup.find_all('iframe'))

            # 21. HasSocialNet
            features['HasSocialNet'] = self._check_social_networks(soup, html_content)

            # 22. HasSubmitButton
            submit_buttons = soup.find_all(['input', 'button'], {'type': 'submit'}) + soup.find_all('button')
            features['HasSubmitButton'] = 1 if submit_buttons else 0

            # 23. HasHiddenFields
            hidden_fields = soup.find_all('input', {'type': 'hidden'})
            features['HasHiddenFields'] = 1 if hidden_fields else 0

            # 24. HasCopyrightInfo
            features['HasCopyrightInfo'] = self._check_copyright(soup, html_content)

            # 25. NoOfImage
            features['NoOfImage'] = len(soup.find_all('img'))

            # 26. NoOfCSS
            features['NoOfCSS'] = len(soup.find_all('link', {'rel': 'stylesheet'})) + len(soup.find_all('style'))

            # 27. NoOfJS
            features['NoOfJS'] = len(soup.find_all('script'))

            # 28. NoOfSelfRef
            features['NoOfSelfRef'] = self._count_self_references(soup, parsed_url.netloc)

            # 29. NoOfEmptyRef
            features['NoOfEmptyRef'] = self._count_empty_references(soup)

            # 30. NoOfExternalRef
            features['NoOfExternalRef'] = self._count_external_references(soup, parsed_url.netloc)

            return features

        except Exception as e:
            print(f"Error extracting features: {e}")
            return self._get_default_features()

    def _calculate_url_similarity_index(self, url: str) -> float:
        """Calculate URL similarity index based on common patterns"""
        suspicious_patterns = [
            r'[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+',  # IP address
            r'[a-zA-Z0-9]+-[a-zA-Z0-9]+-[a-zA-Z0-9]+',  # Multiple hyphens
            r'[a-zA-Z0-9]{20,}',  # Very long strings
        ]

        similarity_score = 0
        for pattern in suspicious_patterns:
            if re.search(pattern, url):
                similarity_score += 0.3

        return min(similarity_score, 1.0)

    def _calculate_char_continuation_rate(self, url: str) -> float:
        """Calculate character continuation rate"""
        if len(url) <= 1:
            return 0

        continuation_count = 0
        for i in range(1, len(url)):
            if url[i] == url[i-1]:
                continuation_count += 1

        return continuation_count / (len(url) - 1)

    def _calculate_tld_legitimate_prob(self, tld: str) -> float:
        """Calculate TLD legitimacy probability"""
        legitimate_tlds = {
            'com': 0.9, 'org': 0.8, 'net': 0.7, 'edu': 0.95, 'gov': 0.98,
            'mil': 0.95, 'int': 0.9, 'co': 0.6, 'uk': 0.8, 'de': 0.8,
            'fr': 0.8, 'jp': 0.8, 'au': 0.8, 'ca': 0.8, 'us': 0.8
        }
        return legitimate_tlds.get(tld, 0.3)

    def _calculate_domain_title_match(self, domain: str, title: str) -> float:
        """Calculate domain-title match score"""
        if not domain or not title:
            return 0

        domain_clean = re.sub(r'[^a-zA-Z0-9]', '', domain.lower())
        title_clean = re.sub(r'[^a-zA-Z0-9]', '', title.lower())

        if not domain_clean or not title_clean:
            return 0

        return difflib.SequenceMatcher(None, domain_clean, title_clean).ratio()

    def _calculate_url_title_match(self, url: str, title: str) -> float:
        """Calculate URL-title match score"""
        if not url or not title:
            return 0

        url_clean = re.sub(r'[^a-zA-Z0-9]', '', url.lower())
        title_clean = re.sub(r'[^a-zA-Z0-9]', '', title.lower())

        if not url_clean or not title_clean:
            return 0

        return difflib.SequenceMatcher(None, url_clean, title_clean).ratio()

    def _check_favicon(self, soup: BeautifulSoup, url: str) -> int:
        """Check if favicon exists"""
        favicon_links = soup.find_all('link', rel=lambda x: x and 'icon' in x.lower())
        if favicon_links:
            return 1

        # Check for default favicon
        try:
            parsed_url = urlparse(url)
            favicon_url = f"{parsed_url.scheme}://{parsed_url.netloc}/favicon.ico"
            response = self.session.head(favicon_url, timeout=5)
            return 1 if response.status_code == 200 else 0
        except:
            return 0

    def _check_responsive(self, soup: BeautifulSoup) -> int:
        """Check if website is responsive"""
        viewport_meta = soup.find('meta', attrs={'name': 'viewport'})
        responsive_classes = soup.find_all(class_=re.compile(r'responsive|mobile|tablet'))
        media_queries = soup.find_all('style', string=re.compile(r'@media'))

        return 1 if viewport_meta or responsive_classes or media_queries else 0

    def _check_social_networks(self, soup: BeautifulSoup, html_content: str) -> int:
        """Check for social network presence"""
        social_patterns = [
            r'facebook\.com', r'twitter\.com', r'instagram\.com', r'linkedin\.com',
            r'youtube\.com', r'pinterest\.com', r'snapchat\.com', r'tiktok\.com'
        ]

        for pattern in social_patterns:
            if re.search(pattern, html_content, re.IGNORECASE):
                return 1

        social_links = soup.find_all('a', href=re.compile(r'(facebook|twitter|instagram|linkedin|youtube|pinterest|snapchat|tiktok)\.com'))
        return 1 if social_links else 0

    def _check_copyright(self, soup: BeautifulSoup, html_content: str) -> int:
        """Check for copyright information"""
        copyright_patterns = [r'©', r'copyright', r'\(c\)', r'&copy;']

        for pattern in copyright_patterns:
            if re.search(pattern, html_content, re.IGNORECASE):
                return 1

        return 0

    def _count_self_references(self, soup: BeautifulSoup, domain: str) -> int:
        """Count self-references in the page"""
        if not domain:
            return 0

        self_refs = 0
        all_links = soup.find_all(['a', 'link', 'script', 'img'], href=True) + soup.find_all(['a', 'link', 'script', 'img'], src=True)

        for link in all_links:
            url = link.get('href') or link.get('src', '')
            if domain in url or url.startswith('/') or url.startswith('./'):
                self_refs += 1

        return self_refs

    def _count_empty_references(self, soup: BeautifulSoup) -> int:
        """Count empty references"""
        empty_refs = 0
        all_links = soup.find_all(['a', 'link', 'script', 'img'])

        for link in all_links:
            href = link.get('href', '')
            src = link.get('src', '')
            if not href and not src or href in ['#', ''] or src in ['#', '']:
                empty_refs += 1

        return empty_refs

    def _count_external_references(self, soup: BeautifulSoup, domain: str) -> int:
        """Count external references"""
        if not domain:
            return 0

        external_refs = 0
        all_links = soup.find_all(['a', 'link', 'script', 'img'], href=True) + soup.find_all(['a', 'link', 'script', 'img'], src=True)

        for link in all_links:
            url = link.get('href') or link.get('src', '')
            if url.startswith('http') and domain not in url:
                external_refs += 1

        return external_refs

    def _get_default_features(self) -> Dict[str, Any]:
        """Return default features when extraction fails"""
        return {
            'TLD': 'unknown', 'Title': '', 'URLLength': 0, 'URLSimilarityIndex': 0, 'CharContinuationRate': 0,
            'TLDLegitimateProb': 0, 'NoOfLettersInURL': 0, 'LetterRatioInURL': 0, 'NoOfDegitsInURL': 0,
            'NoOfOtherSpecialCharsInURL': 0, 'IsHTTPS': 0, 'LineOfCode': 0, 'LargestLineLength': 0,
            'HasTitle': 0, 'DomainTitleMatchScore': 0, 'URLTitleMatchScore': 0,
            'HasFavicon': 0, 'IsResponsive': 0, 'HasDescription': 0, 'NoOfiFrame': 0,
            'HasSocialNet': 0, 'HasSubmitButton': 0, 'HasHiddenFields': 0, 'HasCopyrightInfo': 0,
            'NoOfImage': 0, 'NoOfCSS': 0, 'NoOfJS': 0, 'NoOfSelfRef': 0,
            'NoOfEmptyRef': 0, 'NoOfExternalRef': 0
        }
