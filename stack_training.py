import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import (
    train_test_split, StratifiedKFold, GridSearchCV, cross_val_score
)
from sklearn.preprocessing import StandardScaler, PolynomialFeatures
from sklearn.feature_selection import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, SelectKBest, mutual_info_classif
from sklearn.ensemble import (
    StackingClassifier, RandomForestClassifier, ExtraTreesClassifier
)
from sklearn.linear_model import SGDClassifier
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score, 
    roc_auc_score, confusion_matrix, roc_curve, classification_report
)
from sklearn.pipeline import Pipeline
from sklearn.compose import ColumnTransformer
from category_encoders import TargetEncoder
import lightgbm as lgb
import xgboost as xgb
from scipy.stats import gmean
import joblib
import warnings
warnings.filterwarnings('ignore')

# Set random seed for reproducibility
RANDOM_STATE = 42
np.random.seed(RANDOM_STATE)

class PhishingURLClassifier:
    def __init__(self, random_state=42):
        self.random_state = random_state
        self.selected_features = None
        self.preprocessor = None
        self.stacking_model = None
        self.feature_names = None
        
    def load_and_explore_data(self, filepath):
        """Load and explore the dataset"""
        print("🔹 Loading and exploring data...")
        
        # Load data
        self.df = pd.read_csv(filepath)
        print(f"Dataset shape: {self.df.shape}")
        print(f"Target distribution:\n{self.df['label'].value_counts()}")
        
        # Remove specified columns
        columns_to_drop = ['FILENAME', 'URL', 'Domain']
        existing_columns_to_drop = [col for col in columns_to_drop if col in self.df.columns]
        self.df = self.df.drop(columns=existing_columns_to_drop)
        
        print(f"Shape after dropping columns: {self.df.shape}")
        print(f"Missing values:\n{self.df.isnull().sum().sum()}")
        
        return self.df
    
    def handle_missing_values(self):
        """Handle missing values based on data exploration"""
        print("🔹 Handling missing values...")
        
        # Check for missing values
        missing_info = self.df.isnull().sum()
        if missing_info.sum() > 0:
            print("Missing values found:")
            print(missing_info[missing_info > 0])
            
            # For numerical columns: fill with median
            numerical_cols = self.df.select_dtypes(include=[np.number]).columns
            numerical_cols = numerical_cols.drop('label')  # Exclude target
            
            for col in numerical_cols:
                if self.df[col].isnull().sum() > 0:
                    self.df[col].fillna(self.df[col].median(), inplace=True)
            
            # For categorical columns: fill with mode
            categorical_cols = self.df.select_dtypes(include=['object']).columns
            for col in categorical_cols:
                if self.df[col].isnull().sum() > 0:
                    self.df[col].fillna(self.df[col].mode()[0], inplace=True)
        else:
            print("No missing values found.")
    
    def prepare_features_and_target(self):
        """Separate features and target"""
        print("🔹 Preparing features and target...")
        
        # Separate features and target
        self.X = self.df.drop('label', axis=1)
        self.y = self.df['label']
        
        print(f"Features shape: {self.X.shape}")
        print(f"Target shape: {self.y.shape}")
        
        # Identify categorical columns for encoding
        self.categorical_features = ['TLD', 'Title'] if 'TLD' in self.X.columns and 'Title' in self.X.columns else []
        self.numerical_features = [col for col in self.X.columns if col not in self.categorical_features]
        
        print(f"Categorical features: {self.categorical_features}")
        print(f"Numerical features count: {len(self.numerical_features)}")
    
    def create_preprocessing_pipeline(self):
        """Create preprocessing pipeline with encoding and scaling"""
        print("🔹 Creating preprocessing pipeline...")
        
        # Create preprocessing steps
        preprocessors = []
        
        # Target encoding for categorical features
        if self.categorical_features:
            categorical_transformer = TargetEncoder(smoothing=1.0, min_samples_leaf=1)
            preprocessors.append(('cat', categorical_transformer, self.categorical_features))
        
        # No transformation for numerical features initially (will be scaled later)
        if self.numerical_features:
            from sklearn.preprocessing import FunctionTransformer
            preprocessors.append(('num', FunctionTransformer(), self.numerical_features))
        
        # Create column transformer
        self.preprocessor = ColumnTransformer(
            transformers=preprocessors,
            remainder='drop'
        )
    
    def feature_selection_with_cv(self, X_train, y_train):
        """Apply feature selection with cross-validation to prevent overfitting"""
        print("🔹 Applying feature selection with cross-validation...")
        
        # First apply preprocessing to get encoded features
        X_train_encoded = self.preprocessor.fit_transform(X_train, y_train)
        
        # Get feature names after preprocessing
        feature_names = []
        if self.categorical_features:
            feature_names.extend(self.categorical_features)
        feature_names.extend(self.numerical_features)
        
        # Step 1: Variance Threshold
        print("Applying Variance Threshold...")
        var_threshold = VarianceThreshold(threshold=0.01)  # Remove features with very low variance
        X_var_selected = var_threshold.fit_transform(X_train_encoded)
        
        # Get selected feature indices
        var_selected_idx = var_threshold.get_support()
        selected_feature_names = [feature_names[i] for i in range(len(feature_names)) if var_selected_idx[i]]
        
        print(f"Features after variance threshold: {X_var_selected.shape[1]}")
        
        # Step 2: SelectKBest with cross-validation
        print("Applying SelectKBest with mutual information...")
        
        # Use cross-validation to select stable features
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=self.random_state)
        feature_scores = []
        
        for train_idx, val_idx in cv.split(X_var_selected, y_train):
            X_fold_train = X_var_selected[train_idx]
            y_fold_train = y_train.iloc[train_idx]
            
            selector = SelectKBest(score_func=mutual_info_classif, k=30)
            selector.fit(X_fold_train, y_fold_train)
            feature_scores.append(selector.scores_)
        
        # Average scores across folds
        avg_scores = np.mean(feature_scores, axis=0)
        
        # Select top 30 features based on average scores
        top_k_indices = np.argsort(avg_scores)[-30:]
        
        # Create final feature selector
        self.feature_selector = SelectKBest(score_func=mutual_info_classif, k=30)
        X_selected = self.feature_selector.fit_transform(X_var_selected, y_train)
        
        # Store selected feature names
        final_selected_idx = self.feature_selector.get_support()
        self.selected_features = [selected_feature_names[i] for i in range(len(selected_feature_names)) if final_selected_idx[i]]
        
        print(f"Final selected features: {len(self.selected_features)}")
        print(f"Selected features: {self.selected_features[:10]}...")  # Show first 10
        
        return X_selected
    
    def create_base_learners(self):
        """Create base learners with anti-overfitting configurations"""
        print("🔹 Creating base learners...")
        
        # LightGBM - configured to prevent overfitting
        lgb_params = {
            'objective': 'binary',
            'metric': 'binary_logloss',
            'boosting_type': 'gbdt',
            'num_leaves': 31,  # Conservative to prevent overfitting
            'learning_rate': 0.1,
            'feature_fraction': 0.8,  # Feature sampling
            'bagging_fraction': 0.8,  # Data sampling
            'bagging_freq': 5,
            'min_data_in_leaf': 20,  # Minimum samples per leaf
            'lambda_l1': 0.1,  # L1 regularization
            'lambda_l2': 0.1,  # L2 regularization
            'random_state': self.random_state,
            'n_jobs': -1,
            'verbose': -1
        }
        
        lgb_model = lgb.LGBMClassifier(**lgb_params, n_estimators=100)
        
        # XGBoost - configured to prevent overfitting
        xgb_model = xgb.XGBClassifier(
            n_estimators=100,
            max_depth=4,  # Shallow trees
            learning_rate=0.1,
            subsample=0.8,  # Data sampling
            colsample_bytree=0.8,  # Feature sampling
            reg_alpha=0.1,  # L1 regularization
            reg_lambda=0.1,  # L2 regularization
            random_state=self.random_state,
            n_jobs=-1,
            eval_metric='logloss'
        )
        
        # Random Forest - conservative settings
        rf_model = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,  # Limit depth
            min_samples_split=10,  # Minimum samples to split
            min_samples_leaf=5,  # Minimum samples per leaf
            max_features='sqrt',  # Feature sampling
            random_state=self.random_state,
            n_jobs=-1
        )
        
        # Extra Trees - conservative settings
        et_model = ExtraTreesClassifier(
            n_estimators=100,
            max_depth=10,  # Limit depth
            min_samples_split=10,  # Minimum samples to split
            min_samples_leaf=5,  # Minimum samples per leaf
            max_features='sqrt',  # Feature sampling
            random_state=self.random_state,
            n_jobs=-1
        )
        
        self.base_learners = [
            ('lgb', lgb_model),
            ('xgb', xgb_model),
            ('rf', rf_model),
            ('et', et_model)
        ]
        
        return self.base_learners
    
    def tune_hyperparameters(self, X_train, y_train):
        """Tune hyperparameters with focus on generalization"""
        print("🔹 Tuning hyperparameters with cross-validation...")
        
        tuned_models = []
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=self.random_state)
        
        # LightGBM tuning - conservative grid
        print("Tuning LightGBM...")
        lgb_param_grid = {
            'n_estimators': [50, 100],
            'num_leaves': [15, 31],
            'learning_rate': [0.05, 0.1],
            'min_data_in_leaf': [10, 20]
        }
        
        lgb_base = lgb.LGBMClassifier(
            objective='binary', metric='binary_logloss', boosting_type='gbdt',
            feature_fraction=0.8, bagging_fraction=0.8, bagging_freq=5,
            lambda_l1=0.1, lambda_l2=0.1, random_state=self.random_state,
            n_jobs=-1, verbose=-1
        )
        
        lgb_grid = GridSearchCV(
            lgb_base, lgb_param_grid, cv=cv, scoring='roc_auc',
            n_jobs=-1, verbose=0
        )
        lgb_grid.fit(X_train, y_train)
        tuned_models.append(('lgb', lgb_grid.best_estimator_))
        
        # XGBoost tuning - conservative grid
        print("Tuning XGBoost...")
        xgb_param_grid = {
            'n_estimators': [50, 100],
            'max_depth': [3, 4],
            'learning_rate': [0.05, 0.1],
            'subsample': [0.8, 0.9]
        }
        
        xgb_base = xgb.XGBClassifier(
            colsample_bytree=0.8, reg_alpha=0.1, reg_lambda=0.1,
            random_state=self.random_state, n_jobs=-1, eval_metric='logloss'
        )
        
        xgb_grid = GridSearchCV(
            xgb_base, xgb_param_grid, cv=cv, scoring='roc_auc',
            n_jobs=-1, verbose=0
        )
        xgb_grid.fit(X_train, y_train)
        tuned_models.append(('xgb', xgb_grid.best_estimator_))
        
        # Random Forest tuning
        print("Tuning Random Forest...")
        rf_param_grid = {
            'n_estimators': [50, 100],
            'max_depth': [8, 10],
            'min_samples_split': [10, 15],
            'min_samples_leaf': [5, 10]
        }
        
        rf_base = RandomForestClassifier(
            max_features='sqrt', random_state=self.random_state, n_jobs=-1
        )
        
        rf_grid = GridSearchCV(
            rf_base, rf_param_grid, cv=cv, scoring='roc_auc',
            n_jobs=-1, verbose=0
        )
        rf_grid.fit(X_train, y_train)
        tuned_models.append(('rf', rf_grid.best_estimator_))
        
        # Extra Trees tuning
        print("Tuning Extra Trees...")
        et_param_grid = {
            'n_estimators': [50, 100],
            'max_depth': [8, 10],
            'min_samples_split': [10, 15],
            'min_samples_leaf': [5, 10]
        }
        
        et_base = ExtraTreesClassifier(
            max_features='sqrt', random_state=self.random_state, n_jobs=-1
        )
        
        et_grid = GridSearchCV(
            et_base, et_param_grid, cv=cv, scoring='roc_auc',
            n_jobs=-1, verbose=0
        )
        et_grid.fit(X_train, y_train)
        tuned_models.append(('et', et_grid.best_estimator_))
        
        self.tuned_base_learners = tuned_models
        return tuned_models
    
    def create_meta_learner(self):
        """Create meta-learner with polynomial features"""
        print("🔹 Creating meta-learner...")
        
        # SGD Classifier with L2 regularization
        sgd_base = SGDClassifier(
            loss='log_loss',  # Logistic regression
            penalty='l2',
            alpha=0.01,  # Regularization strength
            random_state=self.random_state,
            max_iter=1000,
            tol=1e-3
        )
        
        # Add polynomial features for interaction terms
        poly_features = PolynomialFeatures(degree=2, interaction_only=True, include_bias=False)
        
        # Create pipeline for meta-learner
        self.meta_learner = Pipeline([
            ('poly', poly_features),
            ('scaler', StandardScaler()),
            ('sgd', sgd_base)
        ])
        
        return self.meta_learner
    
    def build_stacking_ensemble(self, X_train, y_train):
        """Build stacking ensemble with cross-validation"""
        print("🔹 Building stacking ensemble...")
        
        # Create the stacking classifier
        self.stacking_model = StackingClassifier(
            estimators=self.tuned_base_learners,
            final_estimator=self.meta_learner,
            cv=5,  # 5-fold CV for generating meta-features
            stack_method='predict_proba',  # Use probabilities
            n_jobs=-1
        )
        
        # Fit the stacking model
        self.stacking_model.fit(X_train, y_train)
        
        return self.stacking_model
    
    def evaluate_model(self, X_test, y_test):
        """Comprehensive model evaluation"""
        print("🔹 Evaluating model...")
        
        # Predictions
        y_pred = self.stacking_model.predict(X_test)
        y_pred_proba = self.stacking_model.predict_proba(X_test)[:, 1]
        
        # Calculate metrics
        accuracy = accuracy_score(y_test, y_pred)
        precision = precision_score(y_test, y_pred)
        recall = recall_score(y_test, y_pred)
        f1 = f1_score(y_test, y_pred)
        roc_auc = roc_auc_score(y_test, y_pred_proba)
        
        # G-Mean calculation
        cm = confusion_matrix(y_test, y_pred)
        tn, fp, fn, tp = cm.ravel()
        sensitivity = tp / (tp + fn)  # Recall
        specificity = tn / (tn + fp)
        g_mean = np.sqrt(sensitivity * specificity)
        
        # Store results
        self.evaluation_results = {
            'Accuracy': accuracy,
            'Precision': precision,
            'Recall': recall,
            'F1-Score': f1,
            'G-Mean': g_mean,
            'ROC-AUC': roc_auc
        }
        
        # Print results
        print("\n📊 Model Evaluation Results:")
        print("=" * 40)
        for metric, value in self.evaluation_results.items():
            print(f"{metric}: {value:.4f}")
        
        return self.evaluation_results, y_pred, y_pred_proba
    
    def create_visualizations(self, X_test, y_test, y_pred, y_pred_proba):
        """Create and save visualizations"""
        print("🔹 Creating visualizations...")
        
        plt.style.use('default')
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. ROC Curve
        fpr, tpr, _ = roc_curve(y_test, y_pred_proba)
        axes[0, 0].plot(fpr, tpr, color='darkorange', lw=2, 
                       label=f'ROC Curve (AUC = {self.evaluation_results["ROC-AUC"]:.3f})')
        axes[0, 0].plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--')
        axes[0, 0].set_xlim([0.0, 1.0])
        axes[0, 0].set_ylim([0.0, 1.05])
        axes[0, 0].set_xlabel('False Positive Rate')
        axes[0, 0].set_ylabel('True Positive Rate')
        axes[0, 0].set_title('ROC Curve')
        axes[0, 0].legend(loc="lower right")
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. Confusion Matrix
        cm = confusion_matrix(y_test, y_pred)
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=axes[0, 1])
        axes[0, 1].set_title('Confusion Matrix')
        axes[0, 1].set_xlabel('Predicted')
        axes[0, 1].set_ylabel('Actual')
        
        # 3. Feature Importances (from Random Forest base learner)
        if hasattr(self.tuned_base_learners[2][1], 'feature_importances_'):
            rf_importances = self.tuned_base_learners[2][1].feature_importances_
            top_features_idx = np.argsort(rf_importances)[-10:]  # Top 10
            top_features_names = [self.selected_features[i] for i in top_features_idx]
            top_importances = rf_importances[top_features_idx]
            
            axes[1, 0].barh(range(len(top_importances)), top_importances)
            axes[1, 0].set_yticks(range(len(top_importances)))
            axes[1, 0].set_yticklabels(top_features_names)
            axes[1, 0].set_title('Top 10 Feature Importances (Random Forest)')
            axes[1, 0].set_xlabel('Importance')
        
        # 4. Model Performance Comparison
        metrics = list(self.evaluation_results.keys())
        values = list(self.evaluation_results.values())
        
        bars = axes[1, 1].bar(metrics, values, color=['skyblue', 'lightgreen', 'salmon', 'gold', 'plum', 'lightcoral'])
        axes[1, 1].set_title('Model Performance Metrics')
        axes[1, 1].set_ylabel('Score')
        axes[1, 1].set_ylim(0, 1)
        
        # Add value labels on bars
        for bar, value in zip(bars, values):
            height = bar.get_height()
            axes[1, 1].text(bar.get_x() + bar.get_width()/2., height + 0.01,
                           f'{value:.3f}', ha='center', va='bottom')
        
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('model_evaluation_plots.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("Visualizations saved as 'model_evaluation_plots.png'")
    
    def save_results_and_model(self):
        """Save evaluation results and trained models"""
        print("🔹 Saving results and models...")
        
        # Save evaluation metrics and selected features
        results_df = pd.DataFrame([self.evaluation_results])
        results_df['Selected_Features'] = ['; '.join(self.selected_features)]
        results_df.to_csv('model_evaluation_results.csv', index=False)
        
        # Create deployment preprocessing pipeline (without feature selection)
        # This pipeline should only contain: target encoding, scaling for selected features
        
        # Create a simplified preprocessor for deployment
        deployment_preprocessor = Pipeline([
            ('target_encoder', TargetEncoder(smoothing=1.0, min_samples_leaf=1)),
            ('scaler', StandardScaler())
        ])
        
        # Fit the deployment preprocessor on the selected features only
        # We need to create a subset of the original training data with only selected features
        print("Creating deployment preprocessor...")
        
        # Save the full stacking model
        joblib.dump(self.stacking_model, 'final_stacking_model.joblib')
        
        # Save the complete preprocessing pipeline (for reference)
        preprocessing_info = {
            'selected_features': self.selected_features,
            'categorical_features': self.categorical_features,
            'numerical_features': self.numerical_features,
            'preprocessor': self.preprocessor
        }
        joblib.dump(preprocessing_info, 'features_preprocessor.joblib')
        
        print("✅ Model and preprocessing pipeline saved successfully!")
        print("   - final_stacking_model.joblib")
        print("   - features_preprocessor.joblib")
        print("   - model_evaluation_results.csv")
    
    def train_complete_pipeline(self, filepath):
        """Execute the complete training pipeline"""
        print("🚀 Starting Phishing URL Classification Pipeline")
        print("=" * 60)
        
        # 1. Data Preparation
        self.load_and_explore_data(filepath)
        self.handle_missing_values()
        self.prepare_features_and_target()
        self.create_preprocessing_pipeline()
        
        # 2. Data Splitting
        print("🔹 Splitting data...")
        X_train, X_test, y_train, y_test = train_test_split(
            self.X, self.y, test_size=0.2, stratify=self.y, 
            random_state=self.random_state
        )
        
        print(f"Training set shape: {X_train.shape}")
        print(f"Test set shape: {X_test.shape}")
        
        # 3. Feature Selection with CV
        X_train_selected = self.feature_selection_with_cv(X_train, y_train)
        
        # Apply same preprocessing and selection to test set
        X_test_encoded = self.preprocessor.transform(X_test)
        # Apply variance threshold
        var_threshold = VarianceThreshold(threshold=0.01)
        var_threshold.fit(self.preprocessor.transform(X_train))
        X_test_var = var_threshold.transform(X_test_encoded)
        # Apply feature selection
        X_test_selected = self.feature_selector.transform(X_test_var)
        
        # 4. Scaling
        print("🔹 Applying feature scaling...")
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train_selected)
        X_test_scaled = scaler.transform(X_test_selected)
        
        # 5. Model Training
        self.create_base_learners()
        self.tune_hyperparameters(X_train_scaled, y_train)
        self.create_meta_learner()
        self.build_stacking_ensemble(X_train_scaled, y_train)
        
        # 6. Evaluation
        results, y_pred, y_pred_proba = self.evaluate_model(X_test_scaled, y_test)
        
        # 7. Visualizations
        self.create_visualizations(X_test_scaled, y_test, y_pred, y_pred_proba)
        
        # 8. Save Results and Models
        self.save_results_and_model()
        
        print("\n🎉 Pipeline completed successfully!")
        print("=" * 60)
        
        return results

# Usage Example
if __name__ == "__main__":
    # Initialize classifier
    classifier = PhishingURLClassifier(random_state=42)
    
    # Train the complete pipeline
    # Replace 'PhiUSIIL_Phishing_URL_Dataset.csv' with actual file path
    results = classifier.train_complete_pipeline('PhiUSIIL_Phishing_URL_Dataset.csv')
    
    print("\n📋 Final Results Summary:")
    print("-" * 30)
    for metric, value in results.items():
        print(f"{metric}: {value:.4f}")