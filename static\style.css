* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

.input-section {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 30px;
}

.input-group {
    display: flex;
    gap: 15px;
    align-items: center;
}

#urlInput {
    flex: 1;
    padding: 15px 20px;
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

#urlInput:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

#analyzeBtn {
    padding: 15px 25px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 1rem;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    white-space: nowrap;
}

#analyzeBtn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

#analyzeBtn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.loading-section {
    text-align: center;
    padding: 40px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 30px;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.result-section, .error-section {
    margin-bottom: 30px;
}

.result-card, .error-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    overflow: hidden;
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.result-header h2 {
    font-size: 1.5rem;
}

.result-icon {
    font-size: 2rem;
}

.result-content {
    padding: 30px;
}

.url-display {
    margin-bottom: 25px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    word-break: break-all;
}

.classification-result {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 30px;
}

.classification, .probability {
    text-align: center;
    padding: 20px;
    border-radius: 10px;
    background: #f8f9fa;
}

.label {
    display: block;
    font-weight: 600;
    margin-bottom: 10px;
    color: #666;
}

.classification-value {
    font-size: 1.5rem;
    font-weight: bold;
    padding: 10px 20px;
    border-radius: 25px;
    display: inline-block;
}

.classification-value.legitimate {
    background: #d4edda;
    color: #155724;
}

.classification-value.phishing {
    background: #f8d7da;
    color: #721c24;
}

.probability-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #667eea;
}

.features-section {
    border-top: 1px solid #e1e5e9;
    padding-top: 25px;
}

.features-section h3 {
    margin-bottom: 15px;
    color: #333;
}

.toggle-btn {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    width: 100%;
    text-align: left;
}

.toggle-btn:hover {
    background: #e9ecef;
}

.features-content {
    margin-top: 15px;
}

.features-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.feature-item {
    display: flex;
    justify-content: space-between;
    padding: 12px 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.feature-name {
    font-weight: 600;
    color: #333;
}

.feature-value {
    color: #666;
    font-family: monospace;
}

.error-card {
    text-align: center;
    padding: 40px;
    color: #721c24;
}

.error-card i {
    font-size: 3rem;
    margin-bottom: 20px;
}

.error-card h3 {
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.retry-btn {
    margin-top: 20px;
    padding: 12px 25px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.retry-btn:hover {
    background: #c82333;
}

.info-section {
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-top: 40px;
}

.info-section h3 {
    text-align: center;
    margin-bottom: 30px;
    font-size: 1.8rem;
    color: #333;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.info-item {
    text-align: center;
    padding: 20px;
}

.info-item i {
    font-size: 2.5rem;
    color: #667eea;
    margin-bottom: 15px;
}

.info-item h4 {
    margin-bottom: 10px;
    color: #333;
}

.info-item p {
    color: #666;
    line-height: 1.6;
}

@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .input-group {
        flex-direction: column;
    }
    
    .classification-result {
        grid-template-columns: 1fr;
    }
    
    .features-list {
        grid-template-columns: 1fr;
    }
}
