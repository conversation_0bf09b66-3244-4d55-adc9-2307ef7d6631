<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phishing URL Detection System</title>
    <link rel="stylesheet" href="/static/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header>
            <h1><i class="fas fa-shield-alt"></i> Phishing URL Detection System</h1>
            <p>Enter a URL to check if it's legitimate or phishing</p>
        </header>

        <main>
            <div class="input-section">
                <form id="urlForm">
                    <div class="input-group">
                        <input 
                            type="url" 
                            id="urlInput" 
                            placeholder="Enter URL (e.g., https://www.google.com)" 
                            required
                        >
                        <button type="submit" id="analyzeBtn">
                            <i class="fas fa-search"></i> Analyze URL
                        </button>
                    </div>
                </form>
            </div>

            <div id="loadingSection" class="loading-section" style="display: none;">
                <div class="spinner"></div>
                <p>Analyzing URL and extracting features...</p>
            </div>

            <div id="resultSection" class="result-section" style="display: none;">
                <div class="result-card">
                    <div class="result-header">
                        <h2>Analysis Result</h2>
                        <div id="resultIcon" class="result-icon"></div>
                    </div>
                    
                    <div class="result-content">
                        <div class="url-display">
                            <strong>URL:</strong> <span id="analyzedUrl"></span>
                        </div>
                        
                        <div class="classification-result">
                            <div class="classification">
                                <span class="label">Classification:</span>
                                <span id="classification" class="classification-value"></span>
                            </div>
                            <div class="probability">
                                <span class="label">Probability:</span>
                                <span id="probability" class="probability-value"></span>
                            </div>
                        </div>

                        <div class="features-section">
                            <h3>Extracted Features</h3>
                            <div class="features-toggle">
                                <button id="toggleFeatures" class="toggle-btn">
                                    <i class="fas fa-chevron-down"></i> Show Features
                                </button>
                            </div>
                            <div id="featuresContent" class="features-content" style="display: none;">
                                <div id="featuresList" class="features-list"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="errorSection" class="error-section" style="display: none;">
                <div class="error-card">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>Error</h3>
                    <p id="errorMessage"></p>
                    <button id="retryBtn" class="retry-btn">Try Again</button>
                </div>
            </div>
        </main>

        <footer>
            <div class="info-section">
                <h3>How it works</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <i class="fas fa-extract"></i>
                        <h4>Feature Extraction</h4>
                        <p>Extracts 30 specific features from the URL including length, TLD, content analysis, and more</p>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-cogs"></i>
                        <h4>Preprocessing</h4>
                        <p>Applies advanced preprocessing including target encoding, feature selection, and scaling</p>
                    </div>
                    <div class="info-item">
                        <i class="fas fa-brain"></i>
                        <h4>ML Prediction</h4>
                        <p>Uses a stacking ensemble model to classify URLs as legitimate or phishing</p>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <script src="/static/script.js"></script>
</body>
</html>
