from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Request
from fastapi.responses import HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jin<PERSON>2Templates
from pydantic import BaseModel
import joblib
import pandas as pd
import numpy as np
from feature_extractor import URLFeatureExtractor
import os
from typing import Dict, Any
import uvicorn

app = FastAPI(title="Phishing URL Detection System", version="1.0.0")

# Mount static files and templates
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# Load models
try:
    preprocessor = joblib.load('features_preprocessor.joblib')
    model = joblib.load('final_stacking_model.joblib')
    print("Models loaded successfully!")
except Exception as e:
    print(f"Error loading models: {e}")
    preprocessor = None
    model = None

# Initialize feature extractor
feature_extractor = URLFeatureExtractor()

class URLRequest(BaseModel):
    url: str

class PredictionResponse(BaseModel):
    url: str
    classification: str
    probability: float
    features: Dict[str, Any]

@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """Serve the main page"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.post("/predict", response_model=PredictionResponse)
async def predict_url(url_request: URLRequest):
    """Predict if a URL is phishing or legitimate"""
    try:
        if not preprocessor or not model:
            raise HTTPException(status_code=500, detail="Models not loaded properly")

        url = url_request.url.strip()
        if not url:
            raise HTTPException(status_code=400, detail="URL cannot be empty")

        # Extract features
        print(f"Extracting features for URL: {url}")
        features = feature_extractor.extract_features(url)

        # Prepare features for prediction
        feature_names = [
            'TLD', 'Title', 'URLLength', 'URLSimilarityIndex', 'CharContinuationRate', 'TLDLegitimateProb',
            'NoOfLettersInURL', 'LetterRatioInURL', 'NoOfDegitsInURL', 'NoOfOtherSpecialCharsInURL',
            'IsHTTPS', 'LineOfCode', 'LargestLineLength', 'HasTitle', 'DomainTitleMatchScore',
            'URLTitleMatchScore', 'HasFavicon', 'IsResponsive', 'HasDescription', 'NoOfiFrame',
            'HasSocialNet', 'HasSubmitButton', 'HasHiddenFields', 'HasCopyrightInfo', 'NoOfImage',
            'NoOfCSS', 'NoOfJS', 'NoOfSelfRef', 'NoOfEmptyRef', 'NoOfExternalRef'
        ]

        # Create DataFrame with features
        feature_values = [features.get(name, 0) for name in feature_names]
        df = pd.DataFrame([feature_values], columns=feature_names)

        print("Features extracted:", features)

        # Preprocess features
        try:
            processed_features = preprocessor.transform(df)
            print(f"Features preprocessed. Shape: {processed_features.shape}")
        except Exception as e:
            print(f"Preprocessing error: {e}")
            raise HTTPException(status_code=500, detail=f"Feature preprocessing failed: {str(e)}")

        # Make prediction
        try:
            prediction_proba = model.predict_proba(processed_features)[0]
            prediction = model.predict(processed_features)[0]

            # Get probability for phishing (class 0) and legitimate (class 1)
            phishing_prob = prediction_proba[0]  # Probability of being phishing
            legitimate_prob = prediction_proba[1]  # Probability of being legitimate

            # Classification based on prediction
            classification = "Legitimate" if prediction == 1 else "Phishing"

            # Use the probability of the predicted class
            probability = legitimate_prob if prediction == 1 else phishing_prob

            print(f"Prediction: {classification}, Probability: {probability:.4f}")

            return PredictionResponse(
                url=url,
                classification=classification,
                probability=round(probability, 4),
                features=features
            )

        except Exception as e:
            print(f"Prediction error: {e}")
            raise HTTPException(status_code=500, detail=f"Model prediction failed: {str(e)}")

    except HTTPException:
        raise
    except Exception as e:
        print(f"Unexpected error: {e}")
        raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "models_loaded": preprocessor is not None and model is not None
    }

@app.get("/features/{url:path}")
async def get_features(url: str):
    """Get extracted features for a URL (for debugging)"""
    try:
        features = feature_extractor.extract_features(url)
        return {"url": url, "features": features}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Feature extraction failed: {str(e)}")

if __name__ == "__main__":
    # Create directories if they don't exist
    os.makedirs("static", exist_ok=True)
    os.makedirs("templates", exist_ok=True)

    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
