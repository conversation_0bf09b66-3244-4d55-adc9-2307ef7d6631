#!/usr/bin/env python3
"""
Startup script for the Phishing Detection System
"""

import os
import sys
import subprocess

def check_requirements():
    """Check if requirements are installed"""
    try:
        import fastapi
        import uvicorn
        import requests
        import pandas
        import joblib
        from bs4 import BeautifulSoup
        import tldextract
        return True
    except ImportError as e:
        print(f"Missing required package: {e}")
        print("Please install requirements first:")
        print("pip install -r requirements.txt")
        return False

def check_models():
    """Check if model files exist"""
    if not os.path.exists('features_preprocessor.joblib'):
        print("Error: features_preprocessor.joblib not found!")
        return False
    
    if not os.path.exists('final_stacking_model.joblib'):
        print("Error: final_stacking_model.joblib not found!")
        return False
    
    return True

def main():
    print("🔍 Phishing URL Detection System")
    print("=" * 40)
    
    # Check requirements
    print("Checking requirements...")
    if not check_requirements():
        sys.exit(1)
    print("✓ All packages installed")
    
    # Check models
    print("Checking model files...")
    if not check_models():
        sys.exit(1)
    print("✓ Model files found")
    
    # Start server
    print("\n🚀 Starting server...")
    print("Server will be available at: http://localhost:8000")
    print("Press Ctrl+C to stop the server")
    print("-" * 40)
    
    try:
        import uvicorn
        uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
    except Exception as e:
        print(f"\n❌ Error starting server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
