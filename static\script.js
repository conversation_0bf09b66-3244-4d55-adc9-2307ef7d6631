document.addEventListener('DOMContentLoaded', function() {
    const urlForm = document.getElementById('urlForm');
    const urlInput = document.getElementById('urlInput');
    const analyzeBtn = document.getElementById('analyzeBtn');
    const loadingSection = document.getElementById('loadingSection');
    const resultSection = document.getElementById('resultSection');
    const errorSection = document.getElementById('errorSection');
    const toggleFeatures = document.getElementById('toggleFeatures');
    const featuresContent = document.getElementById('featuresContent');
    const retryBtn = document.getElementById('retryBtn');

    // Form submission
    urlForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        await analyzeURL();
    });

    // Retry button
    retryBtn.addEventListener('click', function() {
        hideAllSections();
        urlInput.focus();
    });

    // Toggle features visibility
    toggleFeatures.addEventListener('click', function() {
        const isVisible = featuresContent.style.display !== 'none';
        featuresContent.style.display = isVisible ? 'none' : 'block';

        const icon = toggleFeatures.querySelector('i');
        icon.className = isVisible ? 'fas fa-chevron-down' : 'fas fa-chevron-up';

        toggleFeatures.innerHTML = isVisible ?
            '<i class="fas fa-chevron-down"></i> Show Features' :
            '<i class="fas fa-chevron-up"></i> Hide Features';
    });

    async function analyzeURL() {
        const url = urlInput.value.trim();

        if (!url) {
            showError('Please enter a valid URL');
            return;
        }

        // Show loading
        hideAllSections();
        loadingSection.style.display = 'block';
        analyzeBtn.disabled = true;
        analyzeBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Analyzing...';

        try {
            const response = await fetch('/predict', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ url: url })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || 'Analysis failed');
            }

            const result = await response.json();
            showResult(result);

        } catch (error) {
            console.error('Error:', error);
            showError(error.message || 'An error occurred while analyzing the URL');
        } finally {
            analyzeBtn.disabled = false;
            analyzeBtn.innerHTML = '<i class="fas fa-search"></i> Analyze URL';
            loadingSection.style.display = 'none';
        }
    }

    function showResult(result) {
        hideAllSections();

        // Update result content
        document.getElementById('analyzedUrl').textContent = result.url;

        const classificationElement = document.getElementById('classification');
        classificationElement.textContent = result.classification;
        classificationElement.className = `classification-value ${result.classification.toLowerCase()}`;

        document.getElementById('probability').textContent = `${(result.probability * 100).toFixed(2)}%`;

        // Update result icon
        const resultIcon = document.getElementById('resultIcon');
        if (result.classification === 'Legitimate') {
            resultIcon.innerHTML = '<i class="fas fa-check-circle" style="color: #28a745;"></i>';
        } else {
            resultIcon.innerHTML = '<i class="fas fa-exclamation-triangle" style="color: #dc3545;"></i>';
        }

        // Display features
        displayFeatures(result.features);

        resultSection.style.display = 'block';

        // Scroll to result
        resultSection.scrollIntoView({ behavior: 'smooth' });
    }

    function displayFeatures(features) {
        const featuresList = document.getElementById('featuresList');
        featuresList.innerHTML = '';

        // Define feature categories for better organization
        const featureCategories = {
            'URL Structure': [
                'TLD', 'Title', 'URLLength', 'URLSimilarityIndex', 'CharContinuationRate',
                'TLDLegitimateProb', 'NoOfLettersInURL', 'LetterRatioInURL',
                'NoOfDegitsInURL', 'NoOfOtherSpecialCharsInURL', 'IsHTTPS'
            ],
            'Content Analysis': [
                'LineOfCode', 'LargestLineLength', 'HasTitle',
                'DomainTitleMatchScore', 'URLTitleMatchScore', 'HasFavicon',
                'IsResponsive', 'HasDescription'
            ],
            'Page Elements': [
                'NoOfiFrame', 'HasSocialNet', 'HasSubmitButton', 'HasHiddenFields',
                'HasCopyrightInfo', 'NoOfImage', 'NoOfCSS', 'NoOfJS'
            ],
            'References': [
                'NoOfSelfRef', 'NoOfEmptyRef', 'NoOfExternalRef'
            ]
        };

        Object.entries(featureCategories).forEach(([category, featureNames]) => {
            // Create category header
            const categoryHeader = document.createElement('div');
            categoryHeader.className = 'feature-category';
            categoryHeader.innerHTML = `<h4 style="grid-column: 1/-1; margin: 20px 0 10px 0; color: #667eea; border-bottom: 2px solid #667eea; padding-bottom: 5px;">${category}</h4>`;
            featuresList.appendChild(categoryHeader);

            // Add features in this category
            featureNames.forEach(featureName => {
                if (features.hasOwnProperty(featureName)) {
                    const featureItem = document.createElement('div');
                    featureItem.className = 'feature-item';

                    let displayValue = features[featureName];

                    // Format specific features for better readability
                    if (typeof displayValue === 'number') {
                        if (featureName.includes('Ratio') || featureName.includes('Score') || featureName.includes('Prob')) {
                            displayValue = displayValue.toFixed(4);
                        } else if (Number.isInteger(displayValue)) {
                            displayValue = displayValue.toString();
                        } else {
                            displayValue = displayValue.toFixed(2);
                        }
                    } else if (typeof displayValue === 'string' && displayValue.length > 50) {
                        displayValue = displayValue.substring(0, 50) + '...';
                    }

                    featureItem.innerHTML = `
                        <span class="feature-name">${formatFeatureName(featureName)}</span>
                        <span class="feature-value">${displayValue}</span>
                    `;

                    featuresList.appendChild(featureItem);
                }
            });
        });
    }

    function formatFeatureName(name) {
        // Convert camelCase to readable format
        return name.replace(/([A-Z])/g, ' $1')
                  .replace(/^./, str => str.toUpperCase())
                  .replace(/No Of/g, 'Number of')
                  .replace(/Tld/g, 'TLD')
                  .replace(/Url/g, 'URL')
                  .replace(/Https/g, 'HTTPS')
                  .replace(/Css/g, 'CSS')
                  .replace(/Js/g, 'JS');
    }

    function showError(message) {
        hideAllSections();
        document.getElementById('errorMessage').textContent = message;
        errorSection.style.display = 'block';
        errorSection.scrollIntoView({ behavior: 'smooth' });
    }

    function hideAllSections() {
        loadingSection.style.display = 'none';
        resultSection.style.display = 'none';
        errorSection.style.display = 'none';

        // Reset features toggle
        featuresContent.style.display = 'none';
        toggleFeatures.innerHTML = '<i class="fas fa-chevron-down"></i> Show Features';
    }

    // Add some example URLs for testing (optional)
    const exampleUrls = [
        'https://www.google.com',
        'https://www.github.com',
        'https://www.stackoverflow.com'
    ];

    // Add placeholder rotation (optional enhancement)
    let placeholderIndex = 0;
    setInterval(() => {
        if (document.activeElement !== urlInput) {
            urlInput.placeholder = `Enter URL (e.g., ${exampleUrls[placeholderIndex]})`;
            placeholderIndex = (placeholderIndex + 1) % exampleUrls.length;
        }
    }, 3000);
});
